//
//  ParkingLocationCard.swift
//  XYZ Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import SwiftUI

struct ParkingLocationCard: View {
    let imageName: String
    let title: String
    let address: String
    let price: String
    let spots: String
    let isTopCard: Bool
    let onTap: () -> Void

    var body: some View {
        HStack(spacing: 12) {
            // Parking image
            AsyncImage(url: URL(string: "https://picsum.photos/80/60?random=\(imageName)")) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                Rectangle()
                    .fill(Color(.systemGray5))
                    .overlay(
                        Image(systemName: "car.fill")
                            .foregroundStyle(.secondary)
                    )
            }
            .frame(width: 80, height: 60)
            .clipShape(RoundedRectangle(cornerRadius: 8))
            
            // Parking details
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundStyle(.primary)
                    .lineLimit(1)
                
                Text(address)
                    .font(.caption)
                    .foregroundStyle(.secondary)
                    .lineLimit(2)
                
                HStack(spacing: 16) {
                    HStack(spacing: 2) {
                        Text(price)
                            .font(.subheadline)
                            .fontWeight(.bold)
                            .foregroundStyle(.blue)
                        Text("/hr")
                            .font(.caption2)
                            .foregroundStyle(.secondary)
                    }
                    
                    HStack(spacing: 2) {
                        Text(spots)
                            .font(.subheadline)
                            .fontWeight(.bold)
                            .foregroundStyle(.green)
                        Text("spots")
                            .font(.caption2)
                            .foregroundStyle(.secondary)
                    }
                }
            }
            
            Spacer()
            
            // Arrow indicator
            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundStyle(.secondary)
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.thinMaterial)
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
        .onTapGesture {
            onTap()
        }
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 16) {
        ParkingLocationCard(
            imageName: "parking_1",
            title: "Central Parking",
            address: "123 Collins Street, Melbourne VIC 3000",
            price: "$15",
            spots: "25",
            isTopCard: true,
            onTap: {}
        )

        ParkingLocationCard(
            imageName: "parking_2",
            title: "City Square Parking",
            address: "456 Bourke Street, Melbourne VIC 3000",
            price: "$12",
            spots: "18",
            isTopCard: false,
            onTap: {}
        )
    }
    .padding()
    .background(Color(.systemGray6))
}
