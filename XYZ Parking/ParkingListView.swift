//
//  ParkingListView.swift
//  XYZ Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import SwiftUI

struct ParkingListView: View {
    let parkingLocations: [ParkingLocation]
    let searchText: String
    @Binding var selectedParkingFromMap: ParkingLocation?
    let onParkingLocationSelected: (ParkingLocation) -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            // Section title
            HStack {
                Text("Parking Nearby")
                    .font(.title3)
                    .fontWeight(.bold)
                    .foregroundStyle(.primary)
                Spacer()
            }
            .padding(.horizontal, 20)

            // Parking list
            LazyVStack(spacing: 16) {
                if parkingLocations.isEmpty {
                    VStack(spacing: 12) {
                        Image(systemName: "magnifyingglass")
                            .font(.system(size: 40))
                            .foregroundStyle(.secondary)
                        Text("No parking locations found")
                            .font(.headline)
                            .foregroundStyle(.secondary)
                        Text("Try adjusting your search terms")
                            .font(.caption)
                            .foregroundStyle(.secondary)
                    }
                    .padding(.top, 40)
                } else {
                    ForEach(Array(parkingLocations.enumerated()), id: \.element.id) { index, location in
                        ParkingLocationCard(
                            imageName: "parking_\(location.id)",
                            title: location.name,
                            address: location.address,
                            price: location.price,
                            spots: location.spots,
                            isTopCard: index == 0,
                            onTap: {
                                // 直接调用回调，不再使用导航
                                onParkingLocationSelected(location)
                            }
                        )
                    }
                }
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 20)
        }
    }
}

// MARK: - Preview
#Preview {
    ParkingListView(
        parkingLocations: [
            ParkingLocation(
                id: 1,
                name: "Central Parking",
                address: "123 Collins Street, Melbourne VIC 3000",
                price: "$15",
                spots: "25",
                latitude: -37.8136,
                longitude: 144.9631
            ),
            ParkingLocation(
                id: 2,
                name: "City Square Parking",
                address: "456 Bourke Street, Melbourne VIC 3000",
                price: "$12",
                spots: "18",
                latitude: -37.8140,
                longitude: 144.9633
            )
        ],
        searchText: "",
        selectedParkingFromMap: .constant(nil),
        onParkingLocationSelected: { _ in }
    )
}
